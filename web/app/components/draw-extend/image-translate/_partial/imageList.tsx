import { memo } from 'react'
import cn from 'classnames'
import { useImageTranslateStore, useStore } from '../store'
import type { ImageData } from './imageData'
import { useCustomTranslation } from '@/app/components/draw-extend/helper'
import { uploadAndTranslate } from '@/service/mcp-translate'
import s from '@/app/components/draw-extend/common/loader.module.css'
import Toast from '@/app/components/base/toast'
import { MultiLayerEditorTask } from '@gaia-x/gaia-x-components'
import ScreenModal from '@/app/components/draw-extend/utils/modal'

const ImageBox = memo((props: {
  imageItem: ImageData
  onToggleSelect: (id: number) => void
  onDownload: (id: number) => void
  onOpen: (id: number) => void
}) => {
  const local_t = useCustomTranslation()

  return (
    <div
      className="relative m-2 flex h-48 w-48 cursor-pointer items-center justify-center rounded-xl bg-slate-200"
      onClick={() => props.onOpen(props.imageItem.imageId)}
    >
      {props.imageItem.resultUrl && (
        <>
          <div className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-xl bg-black/40 opacity-0 transition-opacity hover:opacity-100">
            <div className="margin-auto text-md absolute flex flex-col items-center text-white">{local_t('view')}</div>
          </div>
          <img
            src={props.imageItem.resultUrl}
            className="pointer-events-none h-full w-full rounded-xl object-contain"
            alt="translated image"
          />
        </>
      )}

      {props.imageItem.isLoading && (
        <div className={cn(s.loader)}></div>
      )}

      {props.imageItem.selectable && (
        <div
          className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-xl bg-blue-600 transition-opacity"
          style={{ opacity: props.imageItem.selected ? 0.5 : 0 }}
          onClick={(e) => {
            e.stopPropagation()
            props.onToggleSelect(props.imageItem.imageId)
          }}
        >
        </div>
      )}

      {!props.imageItem.isLoading && props.imageItem.resultUrl && !props.imageItem.selectable && (
        <div className="absolute bottom-2 right-2 flex flex-row">
          <div
            className="text-md rounded-md bg-white px-2 py-1 text-slate-600 hover:bg-indigo-50 hover:text-black"
            onClick={(e) => {
              e.stopPropagation()
              props.onDownload(props.imageItem.imageId)
            }}
          >
            {local_t('downloadImage')}
          </div>
        </div>
      )}
    </div>
  )
})

const ImageUploader = memo((props: {
  useContext: any
  useStore: any
  listCache: React.MutableRefObject<ImageData[]>
}) => {
  const local_t = useCustomTranslation()
  const imageTranslateStore = useImageTranslateStore()
  const targetLang = useStore(state => state.targetLang)
  const sourceLang = useStore(state => state.sourceLang)

  const { setImageItems } = imageTranslateStore.getState()

  const updateImageViews = () => {
    // 只有当缓存中的数据与当前状态不同时才更新
    const currentItems = imageTranslateStore.getState().imageItems
    const hasChanges = JSON.stringify(props.listCache.current) !== JSON.stringify(currentItems)
    if (hasChanges)
      setImageItems([...props.listCache.current])
  }

  const processImage = async (imageData: ImageData) => {
    const file = imageData.imageFile
    if (!file)
      return

    try {
      // 使用MCP翻译接口
      const result = await uploadAndTranslate(file, targetLang, sourceLang)

      const index = props.listCache.current.findIndex(item => item.instanceId === imageData.instanceId)
      if (index !== -1) {
        props.listCache.current[index] = {
          ...props.listCache.current[index],
          resultUrl: result.translatedImageUrl,
          isLoading: false,
          translateData: {
            imageEditor: '',
            imageEditorId: '',
            image_url: result.imageUrl,
            mcp_translation_data: result,
          },
        }
        updateImageViews()
      }
    }
    catch {
      // Remove failed item
      props.listCache.current = props.listCache.current.filter(item => item.instanceId !== imageData.instanceId)
      updateImageViews()
    }
  }

  const appendImages = (files: FileList) => {
    const { taskCount, setTaskCount, targetLang, sourceLang } = imageTranslateStore.getState()
    let localTaskCount = taskCount

    // 为这批上传的图片创建唯一标识
    const processBatchId = Date.now().toString()

    Array.from(files).forEach(async (file) => {
      if (file.type.startsWith('image/')) {
        const imageData: ImageData = {
          imageFile: file,
          resultUrl: '',
          isLoading: true,
          imageId: localTaskCount,
          instanceId: `${file.name}-${file.size}-${file.lastModified}-${Date.now()}`,
          selectable: false,
          selected: false,
          targetLang,
          sourceLang,
          componentId: 'image-translate',
          processBatchId,
        }
        localTaskCount += 1

        props.listCache.current.push(imageData)
        updateImageViews()
        await processImage(imageData)
      }
    })
    setTaskCount(localTaskCount)
  }

  const pickFiles = () => {
    if (targetLang === '') {
      Toast.notify({ type: 'error', message: local_t('selectTargetLanguage') })
      return
    }
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.multiple = true
    fileInput.accept = 'image/*'
    fileInput.onchange = (event) => {
      const files = (event.target as HTMLInputElement).files
      if (files && files.length > 0)
        appendImages(files)
    }
    fileInput.click()
  }

  return (
    <div className="m-2 flex h-48 w-48 items-center justify-center rounded-xl bg-white">
      <div
        className="flex h-44 w-44 cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed border-gray-300 bg-gray-50 hover:border-indigo-300"
        onClick={pickFiles}
      >
        <p className="px-2 text-center text-sm text-gray-400">
          {local_t('uploadImageHint')}
        </p>
      </div>
    </div>
  )
})

const ImageList = memo((props: {
  listCache: React.MutableRefObject<ImageData[]>
}) => {
  const imageItems = useStore(state => state.imageItems)
  const imageTranslateStore = useImageTranslateStore()

  const handleToggleSelect = (imageId: number) => {
    const { setImageItems } = imageTranslateStore.getState()
    const updatedItems = imageItems.map((item) => {
      if (item.imageId === imageId && item.componentId === 'image-translate')
        return { ...item, selected: !item.selected }
      return item
    })

    // 只有当选择状态真正发生变化时才更新
    const hasChanges = updatedItems.some((item, index) =>
      item.selected !== imageItems[index]?.selected,
    )

    if (hasChanges) {
      props.listCache.current = updatedItems
      setImageItems(updatedItems)
    }
  }

  const handleOpen = async (imageId: number) => {
    const item = imageItems.find(item => item.imageId === imageId && item.componentId === 'image-translate')
    if (!item?.resultUrl || !item.imageFile)
      return

    const {
      setCurrentImageFile,
      setResizedOrig,
      setMaskingResult,
      setTargetLang,
      setSourceLang,
      setShowModal,
    } = imageTranslateStore.getState()

    // 设置当前预览的图片信息
    setCurrentImageFile(item.imageFile)
    // 使用服务器上传后的URL地址而不是本地浏览器地址
    const originalImageUrl = item.translateData?.image_url || URL.createObjectURL(item.imageFile)
    console.log('item.translateData:', item.translateData)
    console.log('使用的原始图片URL:', originalImageUrl)
    setResizedOrig(originalImageUrl)
    setMaskingResult(item.resultUrl)
    setTargetLang(item.targetLang)
    setSourceLang(item.sourceLang)
    setShowModal(true)
  }

  const handleDownload = async (imageId: number) => {
    const item = imageItems.find(item => item.imageId === imageId && item.componentId === 'image-translate')
    if (!item?.resultUrl || !item.imageFile)
      return

    try {
      console.log('开始下载图片:', item.resultUrl)

      // 创建图片元素
      const img = new Image()

      // 设置跨域属性以避免 CORS 问题
      img.crossOrigin = 'anonymous'

      // 使用 Promise 包装图片加载
      const loadImage = new Promise<string>((resolve, reject) => {
        img.onload = () => {
          try {
            // 创建 canvas 元素
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            if (!ctx) {
              reject(new Error('无法创建 canvas context'))
              return
            }

            // 设置 canvas 尺寸与图片相同
            canvas.width = img.naturalWidth
            canvas.height = img.naturalHeight

            // 将图片绘制到 canvas 上
            ctx.drawImage(img, 0, 0)

            // 转换为 base64 格式
            const base64Data = canvas.toDataURL('image/png')
            resolve(base64Data)
          }
          catch (canvasError) {
            reject(canvasError)
          }
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }
      })

      // 开始加载图片
      img.src = item.resultUrl

      // 等待图片加载并转换为 base64
      const base64Data = await loadImage

      // 使用图片自身的目标语言
      const fileName = `${item.imageFile.name.replace(/\.[^/.]+$/, '')}_${item.targetLang}.png`

      // 创建下载链接
      const link = document.createElement('a')
      link.href = base64Data
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      console.log('图片下载完成:', fileName)
    }
    catch (error) {
      console.error('下载图片时出错:', error)
      Toast.notify({
        type: 'error',
        message: '下载失败，请重试',
      })
    }
  }

  // 获取当前预览的图片数据
  // eslint-disable-next-line complexity
  const getCurrentPreviewData = () => {
    const {
      currentImageFile,
      resizedOrig,
      maskingResult,
      targetLang,
      sourceLang,
    } = imageTranslateStore.getState()

    if (!currentImageFile || !maskingResult) return null

    // 查找当前预览的图片项，获取翻译数据
    const currentItem = imageItems.find(item =>
      item.imageFile === currentImageFile && item.resultUrl === maskingResult,
    )

    // 构建 MultiLayerEditorTask 需要的任务数据
    const taskData: any = {
      origin_url: resizedOrig,
      image_url: maskingResult,
      image_layers: '', // 默认空字符串
      data: {
        imageEditor: '',
        imageEditorId: '',
      },
      description: `翻译图片 - 从 ${sourceLang} 到 ${targetLang}`,
    }

    console.log('getCurrentPreviewData - resizedOrig:', resizedOrig)
    console.log('getCurrentPreviewData - taskData:', taskData)

    // 如果有翻译数据中的图层信息，使用它
    if (currentItem?.translateData?.mcp_translation_data?.fullData?.result?.data?.image_details?.data?.imageEditor) {
      const imageEditorData = currentItem.translateData.mcp_translation_data.fullData.result.data.image_details.data.imageEditor
      taskData.image_layers = imageEditorData
      taskData.data.imageEditor = imageEditorData
      taskData.data.imageEditorId = currentItem.translateData.mcp_translation_data.fullData.result.data.image_details.data.imageEditorId
    }

    return taskData
  }

  // 处理 MultiLayerEditorTask 的提交
  const handleMultiLayerEditorSubmit = async (submitData: any) => {
    console.log('MultiLayerEditor 提交数据:', submitData)

    try {
      // 获取当前预览的图片项
      const {
        currentImageFile,
        maskingResult,
        setShowModal,
      } = imageTranslateStore.getState()

      const currentItem = imageItems.find(item =>
        item.imageFile === currentImageFile && item.resultUrl === maskingResult,
      )

      if (currentItem) {
        console.log('找到当前编辑的图片项:', currentItem)

        // 确定要使用的新图片URL
        let newImageUrl = currentItem.resultUrl // 默认使用原来的URL

        // 检查提交数据中的图片信息
        if (submitData.image_url) {
          if (submitData.image_url.startsWith('data:')) {
            // 如果是 base64 数据，直接使用
            newImageUrl = submitData.image_url
            console.log('使用 base64 图片数据')
          }
          else if (submitData.image_url.startsWith('http')) {
            // 如果是 HTTP URL，直接使用
            newImageUrl = submitData.image_url
            console.log('使用 HTTP URL 图片')
          }
        }
        else if (submitData.imageData) {
          // 如果有 imageData 字段，使用它
          newImageUrl = submitData.imageData
          console.log('使用 imageData 图片数据')
        }

        console.log('原始图片URL:', currentItem.resultUrl)
        console.log('新图片URL:', newImageUrl)

        // 更新本地列表中的图片数据
        const updatedItems = imageItems.map((item) => {
          if (item.imageId === currentItem.imageId && item.componentId === 'image-translate') {
            const updatedItem = {
              ...item,
              resultUrl: newImageUrl, // 使用新的图片URL
              translateData: {
                ...item.translateData,
                imageEditor: item.translateData?.imageEditor || '',
                imageEditorId: item.translateData?.imageEditorId || '',
                image_url: item.translateData?.image_url || '',
                mcp_translation_data: item.translateData?.mcp_translation_data,
                // 保存编辑后的数据
                editedData: submitData,
                lastEditedAt: new Date().toISOString(),
              },
            }
            console.log('更新后的图片项:', updatedItem)
            return updatedItem
          }
          return item
        })

        // 更新缓存和状态
        props.listCache.current = updatedItems
        const { setImageItems } = imageTranslateStore.getState()
        setImageItems(updatedItems)

        // 如果编辑后的图片URL与当前显示的不同，更新预览
        if (newImageUrl !== maskingResult) {
          const { setMaskingResult } = imageTranslateStore.getState()
          setMaskingResult(newImageUrl)
          console.log('更新预览图片:', newImageUrl)
        }

        console.log('图片列表已更新，新图片URL:', newImageUrl)
        console.log('更新后的列表项数量:', updatedItems.length)
      }
      else {
        console.warn('未找到当前编辑的图片项')
      }

      // 关闭弹窗
      setShowModal(false)

      Toast.notify({
        type: 'success',
        message: '图片编辑完成',
      })
    }
    catch (error) {
      console.error('提交失败:', error)
      Toast.notify({
        type: 'error',
        message: '图片编辑失败',
      })
    }
  }

  // 创建自定义的 actions，隐藏跳过按钮
  const createCustomActions = () => {
    return {
      submit: handleMultiLayerEditorSubmit,
      skip: () => {
        // 在图片翻译场景中禁用跳过功能
        Toast.notify({
          type: 'info',
          message: '请完成编辑后再关闭',
        })
      },
    }
  }

  const showModal = useStore(state => state.showModal)
  const { setShowModal } = imageTranslateStore.getState()
  const currentPreviewData = getCurrentPreviewData()

  return (
    <div className="flex flex-row flex-wrap">
      <ImageUploader
        useContext={useImageTranslateStore}
        useStore={useStore}
        listCache={props.listCache}
      />
      {imageItems.map((item: ImageData) => (
        <ImageBox
          key={`${item.instanceId}-${item.resultUrl}`} // 添加 resultUrl 到 key 中确保图片更新时重新渲染
          imageItem={item}
          onToggleSelect={handleToggleSelect}
          onDownload={handleDownload}
          onOpen={handleOpen}
        />
      ))}

      {/* 新的预览弹窗 - 使用 MultiLayerEditorTask */}
      {showModal && currentPreviewData && (
        <ScreenModal
          stayBackground={true}
          show={showModal}
          closeModal={() => setShowModal(false)}
          whiteContainer={false}
        >
          <div className="flex h-full w-full flex-col">
            {/* 工具栏 */}
            <div className="flex border-b bg-white px-8 py-4">
              <div className="flex items-center gap-2">
                {/* 移除了语言选择器，因为 MultiLayerEditorTask 内部已经有了 */}
              </div>
              {/* 关闭按钮 */}
              <div
                className="ml-auto cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={() => setShowModal(false)}
              >
                ✕
              </div>
            </div>

            {/* MultiLayerEditorTask 容器 */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full">

                <MultiLayerEditorTask
                  task={currentPreviewData}
                  actions={createCustomActions()}
                />
              </div>
            </div>
          </div>
        </ScreenModal>
      )}
    </div>
  )
})

export default ImageList
