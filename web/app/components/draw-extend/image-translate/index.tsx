import { memo, useEffect, useRef } from 'react'
import { ImageTranslateContextProvider } from './context'
import type { ImageData } from './_partial/imageData'
import ToolBar from './_partial/toolBar'
import ImageList from './_partial/imageList'
import ResultPreviewModal from './_partial/resultPreview'
import { useCustomTranslation } from '@/app/components/draw-extend/helper'
import { useImageTranslateStore } from './store'

const ImageTranslate: React.FC = () => {
  const local_t = useCustomTranslation()
  const imageItemsCache = useRef<ImageData[]>([])
  const imageTranslateStore = useImageTranslateStore()

  // 在组件挂载时确保所有图片都有组件标识
  useEffect(() => {
    const { imageItems, setImageItems } = imageTranslateStore.getState()

    // 确保现有图片有正确的componentId
    const updatedItems = imageItems.map((item) => {
      if (!item.componentId)
        return { ...item, componentId: 'image-translate' }

      return item
    })

    // 只有当有需要更新的项目时才更新
    const hasUpdates = updatedItems.some((item, index) =>
      item.componentId !== imageItems[index]?.componentId,
    )

    if (hasUpdates)
      setImageItems(updatedItems)
  }, [])

  const LANGUAGE_OPTIONS = [
    { value: 'auto', name: local_t('languages.auto') },
    { value: 'zh-CHS', name: local_t('languages.zh-CHS') },
    { value: 'zh-CHT', name: local_t('languages.zh-CHT') },
    { value: 'en', name: local_t('languages.en') },
    { value: 'ja', name: local_t('languages.ja') },
    { value: 'ko', name: local_t('languages.ko') },
    { value: 'fr', name: local_t('languages.fr') },
    { value: 'es', name: local_t('languages.es') },
    { value: 'pt', name: local_t('languages.pt') },
    { value: 'it', name: local_t('languages.it') },
    { value: 'ru', name: local_t('languages.ru') },
    { value: 'vi', name: local_t('languages.vi') },
    { value: 'de', name: local_t('languages.de') },
    { value: 'ar', name: local_t('languages.ar') },
    { value: 'id', name: local_t('languages.id') },
    { value: 'hi', name: local_t('languages.hi') },
    { value: 'af', name: local_t('languages.af') },
    { value: 'az', name: local_t('languages.az') },
    { value: 'be', name: local_t('languages.be') },
    { value: 'bg', name: local_t('languages.bg') },
    { value: 'bn', name: local_t('languages.bn') },
    { value: 'bs', name: local_t('languages.bs') },
    { value: 'ca', name: local_t('languages.ca') },
    { value: 'ceb', name: local_t('languages.ceb') },
    { value: 'co', name: local_t('languages.co') },
    { value: 'cs', name: local_t('languages.cs') },
    { value: 'cy', name: local_t('languages.cy') },
    { value: 'da', name: local_t('languages.da') },
    { value: 'el', name: local_t('languages.el') },
    { value: 'eo', name: local_t('languages.eo') },
    { value: 'et', name: local_t('languages.et') },
    { value: 'eu', name: local_t('languages.eu') },
    { value: 'fa', name: local_t('languages.fa') },
    { value: 'fi', name: local_t('languages.fi') },
    { value: 'fy', name: local_t('languages.fy') },
    { value: 'ga', name: local_t('languages.ga') },
    { value: 'gd', name: local_t('languages.gd') },
    { value: 'gl', name: local_t('languages.gl') },
    { value: 'gu', name: local_t('languages.gu') },
    { value: 'ha', name: local_t('languages.ha') },
    { value: 'haw', name: local_t('languages.haw') },
    { value: 'he', name: local_t('languages.he') },
    { value: 'hr', name: local_t('languages.hr') },
    { value: 'ht', name: local_t('languages.ht') },
    { value: 'hu', name: local_t('languages.hu') },
    { value: 'hy', name: local_t('languages.hy') },
    { value: 'ig', name: local_t('languages.ig') },
    { value: 'is', name: local_t('languages.is') },
    { value: 'jw', name: local_t('languages.jw') },
    { value: 'ka', name: local_t('languages.ka') },
    { value: 'kk', name: local_t('languages.kk') },
    { value: 'km', name: local_t('languages.km') },
    { value: 'kn', name: local_t('languages.kn') },
    { value: 'ku', name: local_t('languages.ku') },
    { value: 'ky', name: local_t('languages.ky') },
    { value: 'la', name: local_t('languages.la') },
    { value: 'lb', name: local_t('languages.lb') },
    { value: 'lo', name: local_t('languages.lo') },
    { value: 'lt', name: local_t('languages.lt') },
    { value: 'lv', name: local_t('languages.lv') },
    { value: 'mg', name: local_t('languages.mg') },
    { value: 'mi', name: local_t('languages.mi') },
    { value: 'mk', name: local_t('languages.mk') },
    { value: 'ml', name: local_t('languages.ml') },
    { value: 'mn', name: local_t('languages.mn') },
    { value: 'mr', name: local_t('languages.mr') },
    { value: 'ms', name: local_t('languages.ms') },
    { value: 'mt', name: local_t('languages.mt') },
    { value: 'my', name: local_t('languages.my') },
    { value: 'ne', name: local_t('languages.ne') },
    { value: 'nl', name: local_t('languages.nl') },
    { value: 'no', name: local_t('languages.no') },
    { value: 'ny', name: local_t('languages.ny') },
    { value: 'pa', name: local_t('languages.pa') },
    { value: 'pl', name: local_t('languages.pl') },
    { value: 'ps', name: local_t('languages.ps') },
    { value: 'ro', name: local_t('languages.ro') },
    { value: 'sd', name: local_t('languages.sd') },
    { value: 'si', name: local_t('languages.si') },
    { value: 'sk', name: local_t('languages.sk') },
    { value: 'sl', name: local_t('languages.sl') },
    { value: 'sm', name: local_t('languages.sm') },
    { value: 'sn', name: local_t('languages.sn') },
    { value: 'so', name: local_t('languages.so') },
    { value: 'sq', name: local_t('languages.sq') },
    { value: 'sr-Cyrl', name: local_t('languages.sr-Cyrl') },
    { value: 'sr-Latn', name: local_t('languages.sr-Latn') },
    { value: 'st', name: local_t('languages.st') },
    { value: 'su', name: local_t('languages.su') },
    { value: 'sv', name: local_t('languages.sv') },
    { value: 'sw', name: local_t('languages.sw') },
    { value: 'ta', name: local_t('languages.ta') },
    { value: 'te', name: local_t('languages.te') },
    { value: 'tg', name: local_t('languages.tg') },
    { value: 'th', name: local_t('languages.th') },
    { value: 'tl', name: local_t('languages.tl') },
    { value: 'tr', name: local_t('languages.tr') },
    { value: 'uk', name: local_t('languages.uk') },
    { value: 'ur', name: local_t('languages.ur') },
    { value: 'uz', name: local_t('languages.uz') },
    { value: 'yi', name: local_t('languages.yi') },
    { value: 'yo', name: local_t('languages.yo') },
    { value: 'yue', name: local_t('languages.yue') },
    { value: 'zu', name: local_t('languages.zu') },
  ]
  return (
    <div className="flex h-full flex-col pl-0 pr-2 sm:p-2">
      <ToolBar listCache={imageItemsCache} options={LANGUAGE_OPTIONS} />
      <ImageList listCache={imageItemsCache} />
      <ResultPreviewModal />
    </div>
  )
}

const ImageTranslateWrap = memo(ImageTranslate)

const ImageTranslateContainer = () => {
  return (
    <ImageTranslateContextProvider>
      <ImageTranslateWrap/>
    </ImageTranslateContextProvider>
  )
}

export default memo(ImageTranslateContainer)
