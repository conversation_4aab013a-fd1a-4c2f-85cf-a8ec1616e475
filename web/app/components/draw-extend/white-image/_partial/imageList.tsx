import { memo } from 'react'
import cn from 'classnames'
import { useStore, useWhiteImageStore } from '../store'
import type { ImageData } from './imageData'
import { useCustomTranslation } from '@/app/components/draw-extend/helper'
import { resolve_cors, whiteImage } from '@/service/draw-extend'
import s from '@/app/components/draw-extend/common/loader.module.css'
import Toast from '@/app/components/base/toast'

const ImageBox = memo((props: {
  imageItem: ImageData
  onToggleSelect: (id: number) => void
  onDownload: (id: number) => void
  onOpen: (id: number) => void
}) => {
  const local_t = useCustomTranslation()

  return (
    <div
      className="relative m-2 flex h-48 w-48 cursor-pointer items-center justify-center rounded-xl bg-slate-200"
      onClick={() => props.onOpen(props.imageItem.imageId)}
    >
      {props.imageItem.resultUrl && (
        <>
          <div className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-xl bg-black bg-opacity-40 opacity-0 transition transition-opacity hover:opacity-100">
            <div className="margin-auto text-md absolute flex flex-col items-center text-white">{local_t('view')}</div>
          </div>
          <img
            src={resolve_cors(props.imageItem.resultUrl)}
            className="pointer-events-none h-full w-full rounded-xl object-contain"
          />
        </>
      )}

      {props.imageItem.isLoading && (
        <div className={cn(s.loader)}></div>
      )}

      {props.imageItem.selectable && (
        <div
          className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-xl bg-blue-600 transition-opacity"
          style={{ opacity: props.imageItem.selected ? 0.5 : 0 }}
          onClick={(e) => {
            e.stopPropagation()
            props.onToggleSelect(props.imageItem.imageId)
          }}
        >
        </div>
      )}

      {!props.imageItem.isLoading && props.imageItem.resultUrl && !props.imageItem.selectable && (
        <div className="absolute bottom-2 right-2 flex flex-row">
          <div
            className="text-md rounded-md bg-white px-2 py-1 text-slate-600 hover:bg-indigo-50 hover:text-black"
            onClick={(e) => {
              e.stopPropagation()
              props.onDownload(props.imageItem.imageId)
            }}
          >
            {local_t('download')}
          </div>
        </div>
      )}
    </div>
  )
})

const ImageUploader = memo((props: {
  useContext: any
  useStore: any
  listCache: React.MutableRefObject<ImageData[]>
}) => {
  const local_t = useCustomTranslation()
  const whiteImageStore = useWhiteImageStore()
  const canvasSize = useStore(state => state.canvasSize)
  const targetRatio = useStore(state => state.targetRatio)

  const { setImageItems, setPassImages } = whiteImageStore.getState()

  const updateImageViews = () => {
    setImageItems([...props.listCache.current])
  }

  const processImage = async (images: File[]) => {
    try {
      // 记录待处理图片的ID
      const imageIdsToProcess = props.listCache.current
        .filter(item =>
          item.componentId === 'white-image'
          && item.isLoading
          && item.imageFile
          && images.some(f => f.name === (item.imageFile && item.imageFile.name)),
        )
        .map(item => item.imageId)

      const { code, data, success } = await whiteImage(images, canvasSize, targetRatio)

      if (code === 200 && success && data.output_img.length > 0) {
        // 创建文件名到URL的映射
        const nameToUrlMap: Record<string, string> = {}
        images.forEach((file) => {
          const name = `/${file.name.substring(0, file.name.lastIndexOf('.'))}_`
          data.output_img.forEach((url) => {
            if (url.includes(name))
              nameToUrlMap[file.name] = url
          })
        })

        // 更新相应的图片
        let updatedList = [...props.listCache.current].map((item) => {
          if (imageIdsToProcess.includes(item.imageId)) {
            // 根据图片名称查找对应的URL
            const resultUrl = (item.imageFile && nameToUrlMap[item.imageFile.name]) ? nameToUrlMap[item.imageFile.name] : ''
            if (resultUrl) {
              return {
                ...item,
                resultUrl,
                isLoading: false,
              }
            }
          }
          return item
        })

        // 处理跳过的图片
        if (data.pass_img && data.pass_img.length > 0) {
          // 移除跳过的图片并更新状态
          const add_name: string[] = []
          updatedList.forEach((item) => {
            if (item.imageFile && data.pass_img.includes(item.imageFile.name))
              add_name.push(item.imageFile.name)
          })
          updatedList = updatedList.filter(item =>
            (item.imageFile && !data.pass_img.includes(item.imageFile.name)),
          )
          setPassImages(add_name)
        }

        // 更新缓存和视图
        props.listCache.current = updatedList
        updateImageViews()
      }
      else {
        // 移除失败的图片
        const failedItems = imageIdsToProcess.map((id) => {
          const item = props.listCache.current.find(item => item.imageId === id)
          return item?.imageFile?.name || ''
        }).filter(Boolean)

        if (failedItems.length > 0)
          setPassImages(failedItems)

        // 创建新的数组，而不是更新原数组
        props.listCache.current = props.listCache.current.filter(item =>
          !imageIdsToProcess.includes(item.imageId),
        )
        updateImageViews()
      }
    }
    catch (error) {
      console.error('Error processing images:', error)
      updateImageViews()
      Toast.notify({ type: 'error', message: '处理失败' })
    }
  }

  const appendImages = (files: FileList) => {
    const { taskCount, setTaskCount, canvasSize, targetRatio } = whiteImageStore.getState()
    let localTaskCount = taskCount
    const filesToProcess: File[] = []
    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/')) {
        const imageId = localTaskCount++
        const imageData: ImageData = {
          imageFile: file,
          resultUrl: '',
          isLoading: true,
          imageId,
          instanceId: `${file.name}-${Date.now()}-${Math.random()}`,
          selectable: false,
          selected: false,
          canvasSize,
          targetRatio,
          componentId: 'white-image',
        }

        props.listCache.current.push(imageData)
        filesToProcess.push(file)
      }
    })

    if (filesToProcess.length > 0) {
      updateImageViews()
      processImage(filesToProcess)
      setTaskCount(localTaskCount)
    }
  }

  const pickFiles = () => {
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.multiple = true
    fileInput.accept = 'image/*'
    fileInput.onchange = (event) => {
      const files = (event.target as HTMLInputElement).files
      if (files && files.length > 0)
        appendImages(files)
    }
    fileInput.click()
  }

  return (
    <div className="m-2 flex h-48 w-48 items-center justify-center rounded-xl bg-white">
      <div
        className="flex h-44 w-44 cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed border-gray-300 bg-gray-50 hover:border-indigo-300"
        onClick={pickFiles}
        onDragOver={(e) => {
          e.preventDefault()
          e.stopPropagation()
          e.currentTarget.classList.add('border-indigo-500')
        }}
        onDragLeave={(e) => {
          e.preventDefault()
          e.stopPropagation()
          e.currentTarget.classList.remove('border-indigo-500')
        }}
        onDrop={(e) => {
          e.preventDefault()
          e.stopPropagation()
          e.currentTarget.classList.remove('border-indigo-500')
          const files = e.dataTransfer.files
          if (files && files.length > 0)
            appendImages(files)
        }}
      >
        <p className="px-2 text-center text-sm text-gray-400">
          {local_t('uploadImageHint')}
        </p>
      </div>
    </div>
  )
})

const ImageList = memo((props: {
  listCache: React.MutableRefObject<ImageData[]>
  canvasSizeOptions: { value: string; name: string }[]
  onShowDetail?: (img: ImageData) => void
}) => {
  const imageItems = useStore(state => state.imageItems)
  const whiteImageStore = useWhiteImageStore()

  // 确保只显示属于当前组件的图片
  const filteredImageItems = imageItems.filter(item => item.componentId === 'white-image')

  const handleToggleSelect = (imageId: number) => {
    const { setImageItems } = whiteImageStore.getState()
    // 只处理属于白图组件的图片
    props.listCache.current = imageItems.map((item) => {
      if (item.imageId === imageId && item.componentId === 'white-image')
        return { ...item, selected: !item.selected }
      return item
    })
    setImageItems(props.listCache.current)
  }

  const handleOpen = async (imageId: number) => {
    // 只处理属于白图组件的图片
    const item = imageItems.find(item => item.imageId === imageId && item.componentId === 'white-image')
    if (!item?.resultUrl || !item.imageFile)
      return

    const {
      setCurrentImageFile,
      setResultUrl,
      setShowModal,
    } = whiteImageStore.getState()

    // 设置当前预览的图片信息
    setCurrentImageFile(item.imageFile)
    setResultUrl(item.resultUrl)
    setShowModal(true)
  }

  const handleDownload = async (imageId: number) => {
    // 只处理属于白图组件的图片
    const item = imageItems.find(item => item.imageId === imageId && item.componentId === 'white-image')
    if (!item?.resultUrl || !item.imageFile)
      return

    try {
      const response = await fetch(resolve_cors(item.resultUrl))
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`)

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const fileName = item.imageFile.name

      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }
    catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  return (
    <div className="flex grow flex-wrap overflow-y-auto">
      <ImageUploader
        useContext={null}
        useStore={null}
        listCache={props.listCache}
      />
      {filteredImageItems.map(item => (
        <ImageBox
          key={item.imageId}
          imageItem={item}
          onToggleSelect={handleToggleSelect}
          onDownload={handleDownload}
          onOpen={() => {
            handleOpen(item.imageId)
            if (props.onShowDetail) props.onShowDetail(item)
          }}
        />
      ))}
    </div>
  )
})

export default ImageList
