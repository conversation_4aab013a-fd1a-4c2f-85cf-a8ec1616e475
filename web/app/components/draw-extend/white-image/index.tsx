import { memo, useEffect, useRef } from 'react'
import { WhiteImageContextProvider } from './context'
import type { ImageData } from './_partial/imageData'
import ToolBar from './_partial/toolBar'
import ImageList from './_partial/imageList'
import { WhiteImgTask } from '@gaia-x/gaia-x-components'
import { useState } from 'react'
import { useWhiteImageStore } from './store'
import { fetchImageAsBase64 } from '@/app/components/draw-extend/scripts/image'

const WhiteImage: React.FC = () => {
  const imageItemsCache = useRef<ImageData[]>([])
  const whiteImageStore = useWhiteImageStore()
  const [showDetail, setShowDetail] = useState(false)
  const [currentImage, setCurrentImage] = useState<ImageData | null>(null)
  const [isLoadingImage, setIsLoadingImage] = useState(false)

  // 处理完成后写入imageList
  const handleTaskConfirm = (result: { imageData: string, imageName?: string }) => {
    if (!result.imageData) return
    // 新增一条图片到imageList
    const { setImageItems, imageItems } = whiteImageStore.getState()
    const newId = Math.max(0, ...imageItems.map(i => i.imageId)) + 1

    // 创建一个模拟的File对象，确保下载功能正常
    const fileName = result.imageName || `white-img-${Date.now()}.png`
    const mockFile = new File([''], fileName, { type: 'image/png' })

    setImageItems([
      ...imageItems,
      {
        imageFile: mockFile,
        resultUrl: result.imageData,
        isLoading: false,
        imageId: newId,
        instanceId: `${fileName}-${Date.now()}`,
        selectable: false,
        selected: false,
        canvasSize: '',
        targetRatio: 1,
        componentId: 'white-image',
      },
    ])
    setShowDetail(false)
  }

  // 在组件挂载时确保所有图片都有组件标识
  useEffect(() => {
    const { imageItems, setImageItems } = whiteImageStore.getState()

    // 确保现有图片有正确的componentId
    const updatedItems = imageItems.map((item) => {
      if (!item.componentId)
        return { ...item, componentId: 'white-image' }

      return item
    })

    if (JSON.stringify(updatedItems) !== JSON.stringify(imageItems))
      setImageItems(updatedItems)
  }, [])

  // 背景大小选项
  const CANVAS_SIZE_OPTIONS = [
    { value: '(1600,1600)', name: '1600×1600' },
    { value: '(1001,1001)', name: '1001×1001' },
    { value: '(750,1000)', name: '750×1000' },
  ]

  return (
    <div className="flex h-full flex-col pl-0 pr-2 sm:p-2">
      <ToolBar
        listCache={imageItemsCache}
        canvasSizeOptions={CANVAS_SIZE_OPTIONS}
      />
      <ImageList
        listCache={imageItemsCache}
        canvasSizeOptions={CANVAS_SIZE_OPTIONS}
        onShowDetail={async (img: ImageData) => {
          setIsLoadingImage(true)
          try {
            // 将URL转换为base64
            if (img.resultUrl) {
              const base64Data = await fetchImageAsBase64(img.resultUrl)
              setCurrentImage({ ...img, resultUrl: base64Data })
              setShowDetail(true)
            }
          }
          catch (error) {
            console.error('加载图片失败:', error)
          }
          finally {
            setIsLoadingImage(false)
          }
        }}
      />
      {isLoadingImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="text-white">正在加载图片...</div>
        </div>
      )}
      {showDetail && currentImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="relative h-[80%] w-[80%] overflow-auto rounded-lg bg-white p-6 shadow-lg">
            <button
              className="absolute right-2 top-2 z-[100] text-gray-500 hover:text-gray-700"
              onClick={() => setShowDetail(false)}
            >
              关闭
            </button>
            <WhiteImgTask
              task={{ imageData: currentImage.resultUrl, imageName: currentImage.imageFile?.name || 'white-image.png' }}
              hideSkipButton={true}
              actions={{
                submit: (data: any) => {
                  handleTaskConfirm(data)
                  return Promise.resolve()
                },
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

const WhiteImageWrap = memo(WhiteImage)

const WhiteImageContainer = () => {
  return (
    <WhiteImageContextProvider>
      <WhiteImageWrap/>
    </WhiteImageContextProvider>
  )
}

export default memo(WhiteImageContainer)
