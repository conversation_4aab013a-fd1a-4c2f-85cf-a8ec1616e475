'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Toast from '@/app/components/base/toast'
import Input from '@/app/components/base/input'
import Button from '@/app/components/base/button'
import { post } from '@/service/base'
import Link from 'next/link'

// 定义 GVA 登录成功响应类型
type GVALoginResponse = {
  code: number
  data: {
    user: {
      ID: number
      uuid: string
      userName: string
      nickName: string
      headerImg: string
      phone: string
      email: string
      enable: number
      authorityId: number
      authority: {
        authorityId: number
        authorityName: string
        defaultRouter: string
      }
    }
    token: string
    expiresAt: number
    tenantId: string
  }
  msg: string
}

// 定义 GVA 登录请求体类型
type GVALoginBody = {
  username: string
  password: string
  captcha?: string
  captchaId?: string
}

// GVA 登录 API 函数
const loginWithoutTenant = async (data: GVALoginBody): Promise<GVALoginResponse> => {
  return post('/api/base/login', { body: data })
}

export default function GvaLogin() {
  const { t } = useTranslation()
  const router = useRouter()
  // const searchParams = useSearchParams()

  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = async () => {
    if (!username) {
      Toast.notify({ type: 'error', message: t('login.error.emailEmpty') })
      return
    }
    if (!password?.trim()) {
      Toast.notify({ type: 'error', message: t('login.error.passwordEmpty') })
      return
    }

    setIsLoading(true)
    try {
      const loginData: GVALoginBody = {
        username,
        password,
      }

      const res = await loginWithoutTenant(loginData)

      if (res.code === 0 && res.data && typeof res.data === 'object' && 'token' in res.data) {
        localStorage.setItem('console_token', (res.data as any).token)
        localStorage.setItem('tenant_id', (res.data as any).tenantId)

        // 处理重定向逻辑
        if (localStorage.getItem('redirect_url')) {
          const redirectUrl = localStorage.getItem('redirect_url')
          localStorage.removeItem('redirect_url')
          router.replace(redirectUrl as string)
          return
        }
        router.replace('/home')
      }
      else {
        Toast.notify({
          type: 'error',
          message: res.msg,
        })
      }
    }
    catch (error: any) {
      Toast.notify({
        type: 'error',
        message: error.message || t('login.error.loginFailed'),
      })
    }
    finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="mx-auto w-full max-w-md">
      <div className="mb-8">
        <h2 className="mb-2 text-2xl font-bold text-gray-900">
          {t('login.pageTitle')}
        </h2>
        <p className="text-gray-600">
          {t('login.welcome')}
        </p>
      </div>

      <form onSubmit={(e) => {
        e.preventDefault()
        handleLogin()
      }} className="space-y-6">
        <div>
          <label htmlFor="username" className="mb-2 block text-sm font-medium text-gray-700">
            用户名
          </label>
          <Input
            id="username"
            type="text"
            value={username}
            onChange={e => setUsername(e.target.value)}
            placeholder="请输入用户名"
            className="w-full"
            required
          />
        </div>

        <div>
          <label htmlFor="password" className="mb-2 block text-sm font-medium text-gray-700">
            密码
          </label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={e => setPassword(e.target.value)}
              placeholder="请输入密码"
              className="w-full pr-10"
              required
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              )}
            </button>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
          loading={isLoading}
        >
          {isLoading ? '登录中...' : '登录'}
        </Button>
      </form>

      <div className="mt-6 text-center">
        <Link href="/signin" className="text-sm text-blue-600 hover:text-blue-500">
          返回原登录页面
        </Link>
      </div>
    </div>
  )
}
