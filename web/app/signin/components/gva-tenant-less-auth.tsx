import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Toast from '@/app/components/base/toast'
import Input from '@/app/components/base/input'
import Button from '@/app/components/base/button'
import { post } from '@/service/base' // 假设 post 方法从 base 服务中导入
import Link from 'next/link'

// 定义 GVA 登录成功响应类型
export type GVALoginSuccess = {
  code: 0;
  data: {
    user: {
      ID: number;
      uuid: string;
      userName: string;
      nickName: string;
      headerImg: string;
      phone: string;
      email: string;
      enable: number;
      authorityId: number;
      authority: {
        authorityId: number;
        authorityName: string;
        defaultRouter: string;
      };
    };
    token: string;
    expiresAt: number; // Token过期时间戳（毫秒）
    tenantId: string; // 租户ID
  };
  msg: string;
}

// 定义 GVA 登录失败响应类型
export type GVALoginFail = {
  code: number;
  data: {};
  msg: string;
}

// 联合类型，表示登录响应
export type GVALoginResponse = GVALoginSuccess | GVALoginFail

// 登录请求体类型
export type GVALoginBody = {
  username: string;
  password?: string;
  captcha?: string;
  captchaId?: string;
}

// GVALoginWithoutTenant API 调用
const loginWithoutTenant = (body: GVALoginBody): Promise<GVALoginResponse> => {
  return post('/base/loginWithoutTenant', { body })
}

// type _GvaTenantLessAuthProps = {
//   // 可以根据需要添加其他 props
// }

// const _passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/ // 示例密码正则，可根据实际后端要求调整

export default function GvaTenantLessAuth() {
  const { t } = useTranslation()
  // const { locale } = useContext(I18NContext)
  const router = useRouter()
  const searchParams = useSearchParams()

  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  // const [captcha, setCaptcha] = useState('') // 验证码
  // const [captchaId, setCaptchaId] = useState('') // 验证码ID (如果需要)
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // TODO: 后续需要实现验证码的获取和显示

  const handleLogin = async () => {
    if (!username) {
      Toast.notify({ type: 'error', message: t('login.error.emailEmpty') }) // 假设 login.error.emailEmpty 适用于 username
      return
    }
    if (!password?.trim()) {
      Toast.notify({ type: 'error', message: t('login.error.passwordEmpty') })
      return
    }
    // if (!passwordRegex.test(password)) {
    //   Toast.notify({
    //     type: 'error',
    //     message: t('login.error.passwordInvalid'),
    //   });
    //   return;
    // }

    setIsLoading(true)
    try {
      const loginData: GVALoginBody = {
        username,
        password,
        // captcha, // 根据GVA文档，如果需要验证码，这里需要添加
        // captchaId, // 根据GVA文档，如果需要验证码，这里需要添加
      }

      const res = await loginWithoutTenant(loginData)

      if (res.code === 0) {
        localStorage.setItem('console_token', res.data.token) // GVA返回的是 token 而不是 access_token
        // localStorage.setItem('refresh_token', res.data.refresh_token); // GVA文档未提及 refresh_token
        localStorage.setItem('tenant_id', res.data.tenantId) // 存储租户ID

        // 假设存在重定向逻辑
        if (localStorage.getItem('redirect_url')) {
          const redirectUrl = localStorage.getItem('redirect_url')
          localStorage.removeItem('redirect_url')
          router.replace(redirectUrl as string)
          return
        }
        router.replace('/home') // 登录成功后跳转到主页
      }
      else {
        Toast.notify({
          type: 'error',
          message: res.msg, // 使用GVA返回的错误信息
        })
      }
    }
    catch (error: any) {
      Toast.notify({
        type: 'error',
        message: error.message || t('login.error.loginFailed'), // 捕获网络错误或其他异常
      })
    }
    finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      handleLogin()
    }}>
      <div className='mb-3'>
        <label htmlFor="username" className="system-md-semibold my-2 text-text-secondary">
          {t('login.username')} {/* 假设 'login.username' 存在于i18n文件中 */}
        </label>
        <div className="mt-1">
          <Input
            value={username}
            onChange={e => setUsername(e.target.value)}
            id="username"
            type="text"
            autoComplete="username"
            placeholder={t('login.emailPlaceholder') || ''} // 暂时复用 emailPlaceholder
            tabIndex={1}
          />
        </div>
      </div>

      <div className='mb-3'>
        <label htmlFor="password" className="my-2 flex items-center justify-between">
          <span className='system-md-semibold text-text-secondary'>{t('login.password')}</span>
          <Link
            href={`/reset-password?${searchParams.toString()}`}
            className={'system-xs-regular text-components-button-secondary-accent-text'} // 假设一直可用
            tabIndex={0}
          >
            {t('login.forget')}
          </Link>
        </label>
        <div className="relative mt-1">
          <Input
            id="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter')
                handleLogin()
            }}
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            placeholder={t('login.passwordPlaceholder') || ''}
            tabIndex={2}
          />
          <div className="absolute inset-y-0 right-0 flex items-center">
            <Button
              type="button"
              variant='ghost'
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? '👀' : '😝'}
            </Button>
          </div>
        </div>
      </div>

      {/* 验证码输入框，如果GVA需要的话 */}
      {/* <div className='mb-3'>
        <label htmlFor="captcha" className="system-md-semibold my-2 text-text-secondary">
          {t('login.captcha')}
        </label>
        <div className="mt-1">
          <Input
            value={captcha}
            onChange={e => setCaptcha(e.target.value)}
            id="captcha"
            type="text"
            placeholder={t('login.captchaPlaceholder') || ''}
            tabIndex={3}
          />
        </div>
        {captchaId && <img src={`/captcha?captchaId=${captchaId}`} alt="captcha" onClick={fetchNewCaptcha} />}
      </div> */}

      <div className='mb-2'>
        <Button
          tabIndex={4}
          variant='primary'
          onClick={handleLogin}
          disabled={isLoading || !username || !password}
          className="w-full"
        >{t('login.signBtn')}</Button>
      </div>
    </form>
  )
}
