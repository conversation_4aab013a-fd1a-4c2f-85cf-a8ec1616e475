import SocialAuth from './components/social-auth'
import SSOAuth from './components/sso-auth'
import cn from '@/utils/classnames'
import { LicenseStatus, defaultSystemFeatures } from '@/types/feature' // extend：加上 type SystemFeatures
// extend : support ding_talk login
import DingTalkAuth from '@/app/signin/components/dingtalk-auth'
import OAuth2 from '@/app/signin/components/oauth2' // extend: add oauth2
// --- Start GVA Multi-Tenant Login --- //
import GvaTenantLessAuth from './components/gva-tenant-less-auth'
// --- End GVA Multi-Tenant Login --- //

// 声明一个变量来存储钉钉SDK
// const _dd: any = null

const NewLogin = () => {
  const { t } = useTranslation()
  // const router = useRouter()
  const searchParams = useSearchParams()

  const invite_token = decodeURIComponent(searchParams.get('invite_token') || '')
  const isInviteLink = Boolean(invite_token && invite_token !== 'null')

  const [systemFeatures] = useState(defaultSystemFeatures)
  // const [workspaceName, setWorkSpaceName] = useState('')
  // const [allMethodsAreDisabled, setAllMethodsAreDisabled] = useState(false)
  // const [showORLine, setShowORLine] = useState(false)
  // const [authType, setAuthType] = useState<'code' | 'password'>('password')
  const accessToken = decodeURIComponent(searchParams.get('console_token') || '')
  // const refreshToken = decodeURIComponent(searchParams.get('refresh_token') || '')
  // const message = decodeURIComponent(searchParams.get('message') || '')
  // const [isLoading, setIsLoading] = useState(true)

  const renderEmailAuth = () => {
    const canShowEmailAuth = systemFeatures.enable_email_code_login || systemFeatures.enable_email_password_login
    if (!canShowEmailAuth) return null

    return (
      <>
        {/* 替换原有登录组件为 GVA 多租户登录组件 */}
        {/* systemFeatures.enable_email_code_login && authType === 'code' && (
          <>
            <MailAndCodeAuth
              isInvite={isInviteLink}
            />
            {systemFeatures.enable_email_password_login && (
              <div className='cursor-pointer py-1 text-center' onClick={() => setAuthType('password')}>
                <span className='text-sm text-indigo-500 hover:text-indigo-600'>
                  {t('login.usePassword')}
                </span>
              </div>
            )}
          </>
        )}
        {systemFeatures.enable_email_password_login && authType === 'password' && (
          <>
            <MailAndPasswordAuth
              isInvite={isInviteLink}
              isEmailSetup={systemFeatures.email_login_is_setup}
              allowRegistration={systemFeatures.allow_unauthenticated_access}
            />
            {systemFeatures.enable_email_code_login && (
              <div className='cursor-pointer py-1 text-center' onClick={() => setAuthType('code')}>
                <span className='text-sm text-indigo-500 hover:text-indigo-600'>
                  {t('login.useVerificationCode')}
                </span>
              </div>
            )}
          </>
        )} */}
        <GvaTenantLessAuth /> {/* 直接渲染 GVA 多租户登录组件 */}
      </>
    )
  }

  const licenseStatus = systemFeatures.license?.status
  if ([LicenseStatus.LOST, LicenseStatus.EXPIRED, LicenseStatus.INACTIVE].includes(licenseStatus))
    return <LicenseNode t={t} status={licenseStatus} />

  if (isLoading || accessToken) {
    return <div className={
      cn(
        'flex w-full grow flex-col items-center justify-center',
        'px-6',
        'md:px-[108px]',
      )
    }>
      <Loading type='area' />
    </div>
  }

  return (
    <>
      <div className="mx-auto mt-8 w-full">
        {isInviteLink
          ? <div className="mx-auto w-full">
            <h2 className="title-4xl-semi-bold text-text-primary">{t('login.join')}{workspaceName}</h2>
            <p className='body-md-regular mt-2 text-text-tertiary'>{t('login.joinTipStart')}{workspaceName}{t('login.joinTipEnd')}</p>
          </div>
          : <div className="mx-auto w-full">
            <h2 className="title-4xl-semi-bold text-text-primary">{t('login.pageTitle')}</h2>
            <p className='body-md-regular mt-2 text-text-tertiary'>{t('login.welcome')}</p>
          </div>}
        <div className="relative">
          <div className="mt-6 flex flex-col gap-3">
            {systemFeatures.enable_social_oauth_login && <SocialAuth />}
            {systemFeatures.sso_enforced_for_signin && <div className='w-full'>
              <SSOAuth protocol={systemFeatures.sso_enforced_for_signin_protocol} />
            </div>}
            {/* Extend start: ding_talk login */}
            {systemFeatures.ding_talk && (<DingTalkAuth clientId={systemFeatures.ding_talk_client_id}></DingTalkAuth>)}
            { /* Extend stop: DingTalk login */}
            {/* Extend start: oauth2 login */}
            {systemFeatures.is_custom_auth2 && (<OAuth2 title={systemFeatures.is_custom_auth2_button}></OAuth2>)}
            { /* Extend stop: oauth2 login */}
          </div>

          {showORLine && <div className="relative mt-6">
            <div className="absolute inset-0 flex items-center" aria-hidden="true">
              <div className='h-px w-full bg-gradient-to-r from-background-gradient-mask-transparent via-divider-regular to-background-gradient-mask-transparent'></div>
            </div>
            <div className="relative flex justify-center">
              <span className="system-xs-medium-uppercase px-2 text-text-tertiary">{t('login.or')}</span>
            </div>
          </div>}

          {renderEmailAuth()}
        </div>
      </div>
    </>
  )
}

export default NewLogin
