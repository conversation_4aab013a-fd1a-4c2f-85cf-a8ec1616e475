import Toast from '@/app/components/base/toast'
import { post } from './base'

// 定义响应类型
type UploadResponse = {
  data?: {
    file?: {
      url: string
    }
  }
  code?: number
  msg?: string
}

type TranslateResponse = {
  code: number
  msg?: string
  data?: {
    success: boolean
    msg?: string
    result?: {
      success: boolean
      msg?: string
      data?: {
        image_url: string
        image_details?: any
      }
    }
  }
}

// 上传图片到指定服务器
export const uploadImage = async (file: File): Promise<string> => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('classId', '0')

    const response = await post<UploadResponse>(
      '/server/api/fileUploadAndDownload/upload', {
        body: formData,
        headers: new Headers({}), // 让浏览器自动设置正确的 Content-Type
      }, {
        bodyStringify: false, // 重要：FormData 不需要 JSON 序列化
        deleteContentType: true, // 确保不添加 Content-Type 头
        baseUrl: process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX,
      })

    // 检查返回的数据结构
    if (typeof response === 'object' && response.data?.file) {
      return `${process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX}/server/api/${response.data.file.url}`
    }
    else {
      console.error('Upload error: 上传响应格式错误')
      Toast.notify({ type: 'error', message: '图片上传失败' })
      throw new Error('上传响应格式错误')
    }
  }
  catch (error) {
    console.error('Upload error:', error)
    Toast.notify({ type: 'error', message: '图片上传失败' })
    throw error
  }
}

// MCP翻译图片接口
export const translateImage = async (
  imageUrl: string,
  sourceLang: string,
  targetLang: string,
) => {
  try {
    // 创建 AbortController 用于超时控制
    imageUrl = imageUrl.trim()
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 60000) // 60秒超时
    if (!(imageUrl.startsWith('http://') || imageUrl.startsWith('https://')))
      imageUrl = `http://gaia-x.dev.yafex.cn${imageUrl}`

    const response = await post<TranslateResponse>('/server/api/mcp/ai-draw-server-pro/translate_image', {
      body: {
        arguments: {
          imgurl: imageUrl.trim(),
          method: 'aidc',
          aidc_version: 'pro',
          language: targetLang,
          from_code: sourceLang,
          including_product_area: 'false',
          translating_brand_in_product: 'false',
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    }, { baseUrl: process.env.NEXT_PUBLIC_PUBLIC_API_PREFIX })

    clearTimeout(timeoutId)

    // 检查响应结构
    if (response.code !== 0) {
      const errorMsg = response.msg || '翻译请求失败'
      console.error('Translation error: Response code', response.code, errorMsg)
      Toast.notify({ type: 'error', message: errorMsg })
      throw new Error(errorMsg)
    }

    // 处理嵌套的响应数据结构
    const data = response.data
    if (!data || !data.success) {
      const errorMsg = data?.msg || '翻译处理失败'
      console.error('Translation error: Data processing failed', errorMsg)
      Toast.notify({ type: 'error', message: errorMsg })
      throw new Error(errorMsg)
    }

    const resultData = data.result
    if (!resultData || !resultData.success) {
      const errorMsg = resultData?.msg || '翻译处理失败'
      console.error('Translation error: Result processing failed', errorMsg)
      Toast.notify({ type: 'error', message: errorMsg })
      throw new Error(errorMsg)
    }

    const imageData = resultData.data
    if (!imageData || !imageData.image_url) {
      console.error('Translation error: Missing image URL in result')
      Toast.notify({ type: 'error', message: '翻译结果中缺少图片URL' })
      throw new Error('翻译结果中缺少图片URL')
    }

    return {
      success: true,
      translated_image_url: imageData.image_url,
      message: '翻译成功',
      // 保存完整的响应数据用于详情展示
      fullData: data,
      imageDetails: imageData.image_details,
    }
  }
  catch (error) {
    // 如果之前没有处理过这个错误，再处理一次
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        const timeoutError = new Error('翻译请求超时，请重试')
        console.error('Translation timeout:', timeoutError)
        Toast.notify({ type: 'error', message: timeoutError.message })
        throw timeoutError
      }
      else if (!error.message.includes('翻译请求失败') && !error.message.includes('翻译处理失败') && !error.message.includes('翻译结果中缺少图片URL')) {
        console.error('Translation error:', error)
        Toast.notify({ type: 'error', message: '图片翻译失败' })
      }
    }
    throw error
  }
}

// 组合函数：上传图片并翻译
export const uploadAndTranslate = async (
  file: File,
  targetLang: string,
  sourceLang: string = 'auto', // 使用下划线前缀表示未使用的参数
): Promise<{
  imageUrl: string;
  translatedImageUrl: string;
  fullData?: any;
  imageDetails?: any;
}> => {
  try {
    // 1. 上传图片
    const imageUrl = await uploadImage(file)
    // 2. 翻译图片
    const translationResult = await translateImage(imageUrl, sourceLang, targetLang)
    return {
      imageUrl,
      translatedImageUrl: translationResult.translated_image_url,
      fullData: translationResult.fullData,
      imageDetails: translationResult.imageDetails,
    }
  }
  catch (error) {
    console.error('Upload and translate error:', error)
    throw error
  }
}
